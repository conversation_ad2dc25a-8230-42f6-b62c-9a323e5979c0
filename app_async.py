import asyncio
import aiohttp
import aiofiles
import json
import os
import tempfile
import hashlib
import pickle
import re
import traceback
from typing import List, Dict, Any, Optional
from functools import lru_cache
import numpy as np
import faiss
import fitz
from docx import Document
from bs4 import BeautifulSoup
import email
from email import policy
import extract_msg
from openai import AsyncOpenAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from flask import Flask, request, jsonify, Response, stream_template
from flask_cors import CORS
from dotenv import load_dotenv
import redis.asyncio as redis
import uvloop
from redis_config import redis_manager, init_redis_cache, cleanup_redis_cache
from batch_processor import batch_processor, batch_embed_texts, batch_complete_prompt
from performance_optimizations import (
    memory_optimizer, embedding_cache, text_optimizer, chunk_optimizer,
    performance_monitor, FAISSOptimizer, optimize_for_performance,
    get_comprehensive_stats
)

# Set uvloop as the default event loop for better performance
asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

load_dotenv()
app = Flask(__name__)
CORS(app)

# Async OpenAI client
async_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
TEAM_TOKEN = "d1b791fa0ef5092d9cd051b2b09df2473d1e2ea07e09fe6c61abb5722dfbc7d3"

# Redis configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
redis_client = None

# Cache configuration
CACHE_DIR = "cache"
os.makedirs(CACHE_DIR, exist_ok=True)

# Connection pool for HTTP requests
http_session = None

# Pre-compiled regex patterns for better performance
REGEX_PATTERNS = {
    'page_headers': re.compile(r"Page \d+ of \d+"),
    'whitespace': re.compile(r"\s{2,}"),
    'broken_words': re.compile(r"(\w+)-\s*\n\s*(\w+)"),
    'currency': re.compile(r"\$\s+(\d)"),
    'percentage': re.compile(r"(\d)\s+%"),
    'newlines': re.compile(r"\n{3,}"),
    'definitions': re.compile(r'\b([A-Z][\w\s]+)\s+means\s+([^\.]+\.)', re.MULTILINE),
    'first_sentence': re.compile(r'[.!?]\s'),
    'insurance_keywords': re.compile(r'\b(?:claim|premium|coverage|deductible|exclusion|benefit|policy|insured|limit|condition|amount|liability|copay|coinsurance|network|provider|reimbursement|payment|cost|fee|charge|expense|maximum|minimum|percentage|dollar|annual|monthly|eligible|eligibility|waiting|period|effective|date|termination|renewal|grace|notification|approval|document|required|submission|proof|evidence|terms|clause|diagnosis|treatment|procedure|physician|hospital|prescription|medication|emergency|preventive|specialist|definition|scope|coverage|exclusion|exception|provision|endorsement|schedule|attachment|addendum)\b', re.IGNORECASE)
}

async def init_redis():
    """Initialize Redis connection pool with advanced caching"""
    global redis_client
    try:
        # Use the advanced Redis manager
        success = await init_redis_cache(REDIS_URL)
        if success:
            redis_client = redis_manager
            print("✅ Advanced Redis cache initialized successfully")
        else:
            redis_client = None
            print("⚠️ Redis cache initialization failed, using disk cache only")
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        redis_client = None

async def init_http_session():
    """Initialize HTTP session with optimized connection pooling"""
    global http_session

    # Advanced connector configuration for maximum performance
    connector = aiohttp.TCPConnector(
        limit=200,  # Increased total connection pool size
        limit_per_host=50,  # Increased connections per host
        ttl_dns_cache=600,  # Longer DNS cache TTL (10 minutes)
        use_dns_cache=True,
        keepalive_timeout=60,  # Longer keepalive for better reuse
        enable_cleanup_closed=True,
        force_close=False,  # Reuse connections
        ssl=False,  # Disable SSL verification for better performance (use with caution)
    )

    # Optimized timeout configuration
    timeout = aiohttp.ClientTimeout(
        total=120,  # Total timeout
        connect=15,  # Connection timeout
        sock_read=60,  # Socket read timeout
        sock_connect=10  # Socket connection timeout
    )

    # Enhanced session with performance headers
    http_session = aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers={
            'User-Agent': 'Insurance-Assistant-Optimized/2.0',
            'Accept-Encoding': 'gzip, deflate',  # Enable compression
            'Connection': 'keep-alive',  # Explicit keep-alive
            'Cache-Control': 'no-cache'  # Prevent unwanted caching
        },
        # Enable automatic decompression
        auto_decompress=True,
        # Increase read buffer size for better performance
        read_bufsize=65536  # 64KB buffer
    )

async def cleanup_resources():
    """Cleanup resources on shutdown"""
    if http_session:
        await http_session.close()
    if redis_client:
        await cleanup_redis_cache()
    await batch_processor.stop()  # Stop the batch processor

@lru_cache(maxsize=1000)
def clean_text(text):
    """Optimized text cleaning with pre-compiled regex and performance monitoring"""
    # Use the optimized text processor
    text = text_optimizer.fast_clean_text(text)

    # Apply regex patterns
    text = REGEX_PATTERNS['page_headers'].sub("", text)
    text = REGEX_PATTERNS['whitespace'].sub(" ", text)
    text = REGEX_PATTERNS['broken_words'].sub(r"\1\2", text)
    text = REGEX_PATTERNS['currency'].sub(r"$\1", text)
    text = REGEX_PATTERNS['percentage'].sub(r"\1%", text)
    text = REGEX_PATTERNS['newlines'].sub("\n\n", text)
    return text.strip()

@lru_cache(maxsize=1000)
def get_cache_key(url):
    return hashlib.md5(url.encode()).hexdigest()

async def load_cache_async(cache_key: str) -> Optional[Dict]:
    """Load cache with advanced Redis manager, then disk fallback"""
    if redis_client and redis_client.connected:
        # Try Redis first (much faster)
        cached_data = await redis_client.get_document_cache(f"cache_key_{cache_key}")
        if cached_data:
            return cached_data

    # Fallback to disk cache
    path = os.path.join(CACHE_DIR, f"{cache_key}.pkl")
    if os.path.exists(path):
        try:
            async with aiofiles.open(path, "rb") as f:
                data = pickle.loads(await f.read())
                # Store in Redis for next time if available
                if redis_client and redis_client.connected:
                    await redis_client.set_document_cache(f"cache_key_{cache_key}", data)
                return data
        except Exception:
            pass
    return None

async def save_cache_async(cache_key: str, data: Dict):
    """Save cache to both Redis and disk with advanced caching"""
    # Save to Redis (fast access) with advanced manager
    if redis_client and redis_client.connected:
        await redis_client.set_document_cache(f"cache_key_{cache_key}", data)

    # Save to disk (persistence)
    try:
        path = os.path.join(CACHE_DIR, f"{cache_key}.pkl")
        async with aiofiles.open(path, "wb") as f:
            await f.write(pickle.dumps(data))
    except Exception:
        pass  # Don't fail if disk cache fails

async def extract_text_from_url_async(url: str) -> List[str]:
    """Async document extraction with connection pooling"""
    if not http_session:
        await init_http_session()
    
    try:
        async with http_session.get(url) as response:
            response.raise_for_status()
            content = await response.read()
            content_type = response.headers.get("Content-Type", "").lower()
            
            # Extract filename from URL
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            path_parts = parsed_url.path.split('/')
            filename = path_parts[-1].lower() if path_parts and path_parts[-1] else "document.pdf"
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False) as tmp:
                tmp.write(content)
                tmp_path = tmp.name
                
    except Exception as e:
        raise e
    
    # Process document based on type
    try:
        if "pdf" in content_type or filename.endswith(".pdf"):
            doc = fitz.open(tmp_path)
            return [clean_text(page.get_text()) for page in doc]
        
        elif "word" in content_type or filename.endswith(".docx"):
            doc = Document(tmp_path)
            return ["\n".join(clean_text(p.text) for p in doc.paragraphs)]
        
        elif "text/plain" in content_type or filename.endswith(".txt"):
            async with aiofiles.open(tmp_path, 'r', encoding='utf-8') as f:
                content_text = await f.read()
            return [clean_text(content_text)]
        
        elif "html" in content_type or filename.endswith(".html"):
            async with aiofiles.open(tmp_path, 'r', encoding='utf-8') as f:
                html_content = await f.read()
            soup = BeautifulSoup(html_content, "lxml")
            return [clean_text(soup.get_text(separator="\n"))]
        
        elif filename.endswith(".eml"):
            with open(tmp_path, "rb") as f:
                msg = email.message_from_binary_file(f, policy=policy.default)
            body = ""
            if msg.is_multipart():
                for part in msg.walk():
                    if part.get_content_type() == "text/plain":
                        body += part.get_payload(decode=True).decode(errors="ignore")
            else:
                body = msg.get_payload(decode=True).decode(errors="ignore")
            return [clean_text(body)]
        
        elif filename.endswith(".msg"):
            msg = extract_msg.Message(tmp_path)
            return [clean_text(msg.body)]
        
        else:
            raise ValueError("Unsupported document type or unknown format.")
            
    finally:
        # Cleanup temp file
        try:
            os.unlink(tmp_path)
        except:
            pass

def generate_smart_chunks(text_by_page):
    """Generate optimized chunks for insurance documents with performance enhancements"""
    separators = [
        "\n\nARTICLE", "\n\nSECTION", "\n\nCLAUSE", "\n\nPART",
        "\n\nCOVERAGE", "\n\nBENEFIT", "\n\nEXCLUSION", "\n\nLIMIT",
        "\n\nArticle", "\n\nSection", "\n\nClause", "\n\nPart",
        "\n\nCoverage", "\n\nBenefit", "\n\nExclusion", "\n\nLimit",
        r"\n\d+\.\d+", r"\n\d+\.", r"\n[A-Z]\.", r"\n[a-z]\.", r"\n[ivxIVX]+\.",
        "\n\nDefinitions", "\n\nTerms", "\n\nGlossary",
        "\n\n", "\n", ". ", "; ", ", ", " "
    ]

    page_count = len(text_by_page)
    chunk_size = 1200 if page_count >= 100 else 800
    chunk_overlap = 150 if page_count >= 100 else 100

    splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        separators=separators
    )

    chunks = []
    for page_num, page_text in enumerate(text_by_page, 1):
        if not page_text.strip():
            continue

        page_chunks = splitter.split_text(page_text)
        for i, chunk in enumerate(page_chunks):
            if len(chunk.strip()) < 50:
                continue

            first_sentence_match = REGEX_PATTERNS['first_sentence'].split(chunk.strip())
            first_sentence = first_sentence_match[0][:100] if first_sentence_match else chunk.strip()[:100]
            context_prefix = f"Page {page_num}, Section {i+1}: {first_sentence}... "

            chunk_lower = chunk.lower()
            metadata = []
            if 'means' in chunk_lower and bool(re.search(r'\b\w+\s+means\b', chunk_lower)):
                metadata.append("definition")
            if bool(re.search(r'\bexclusion|\bexcluded|\bnot covered|\bnot eligible', chunk_lower)):
                metadata.append("exclusion")
            if bool(re.search(r'\bcoverage|\bcovered|\beligible|\bincluded', chunk_lower)):
                metadata.append("coverage")
            if bool(re.search(r'\blimit|\bcap|\bmaximum|\bupto|\bup to', chunk_lower)):
                metadata.append("limit")
            if bool(re.search(r'\bcondition|\bprovided that|\bsubject to|\bif and only if', chunk_lower)):
                metadata.append("condition")

            chunks.append({
                "text": context_prefix + chunk,
                "page": page_num,
                "section": i+1,
                "raw_text": chunk,
                "metadata": metadata
            })

    # Optimize chunks for memory efficiency
    optimized_chunks = chunk_optimizer.optimize_chunks(chunks)
    return optimized_chunks

async def embed_chunks_openai_async(chunks):
    """Async embedding generation with advanced batching and caching"""
    start_time = time.time()

    # Generate cache key for embeddings
    texts = [c["text"] for c in chunks]
    cache_key = hashlib.md5(str(texts).encode()).hexdigest()

    # Check embedding cache first
    cached_embeddings = await embedding_cache.get_embeddings(cache_key)
    if cached_embeddings is not None:
        performance_monitor.record_cache_hit()
        performance_monitor.record_embedding_time(time.time() - start_time)

        # Build optimized FAISS index
        index = FAISSOptimizer.optimize_index(cached_embeddings)
        return index, chunks, cached_embeddings

    performance_monitor.record_cache_miss()

    try:
        # Use the advanced batch processor for optimal performance
        all_embeddings = await batch_embed_texts(texts, "text-embedding-3-large")

        if not all_embeddings:
            raise ValueError("Failed to generate embeddings")

        embeddings = np.array(all_embeddings, dtype=np.float32)

        # Optimize embeddings for memory efficiency
        embeddings = memory_optimizer.optimize_numpy_arrays([embeddings])[0]
        norm_embeddings = embeddings / np.linalg.norm(embeddings, axis=1)[:, None]

        # Cache the embeddings
        await embedding_cache.set_embeddings(cache_key, norm_embeddings, {
            'chunk_count': len(chunks),
            'created_at': time.time()
        })

        # Build optimized FAISS index
        index = FAISSOptimizer.optimize_index(norm_embeddings)

        embedding_time = time.time() - start_time
        performance_monitor.record_embedding_time(embedding_time)

        return index, chunks, norm_embeddings
    except Exception as e:
        raise e

async def insurance_specific_retrieve_async(question, index, chunks, k=8):
    """Async retrieval with optimized scoring and batching"""
    try:
        # Use batch processor for embedding generation
        embeddings = await batch_embed_texts([question], "text-embedding-3-large")
        if not embeddings:
            raise ValueError("Failed to generate question embedding")

        q_vec = np.array(embeddings[0], dtype=np.float32)
        q_vec = q_vec / np.linalg.norm(q_vec)
    except Exception as e:
        raise e

    # Get more candidates for better matching
    scores, indices = index.search(np.array([q_vec]), k*4)

    # Pre-defined insurance keywords for faster lookup
    insurance_keywords = {
        'claim', 'premium', 'coverage', 'deductible', 'exclusion',
        'benefit', 'policy', 'insured', 'limit', 'condition', 'amount',
        'liability', 'copay', 'coinsurance', 'network', 'provider',
        'reimbursement', 'payment', 'cost', 'fee', 'charge', 'expense',
        'maximum', 'minimum', 'percentage', 'dollar', 'annual', 'monthly',
        'eligible', 'eligibility', 'waiting', 'period', 'effective', 'date',
        'termination', 'renewal', 'grace', 'notification', 'approval', 'document',
        'required', 'submission', 'proof', 'evidence', 'terms', 'clause',
        'diagnosis', 'treatment', 'procedure', 'physician', 'hospital',
        'prescription', 'medication', 'emergency', 'preventive', 'specialist',
        'definition', 'scope', 'coverage', 'exclusion', 'exception', 'provision',
        'endorsement', 'schedule', 'attachment', 'addendum'
    }

    question_lower = question.lower()
    question_words = set(re.findall(r"\w+", question_lower))
    question_words_list = list(question_words)
    question_bigrams = set([' '.join(pair) for pair in zip(question_words_list, question_words_list[1:])])

    question_has_numbers = bool(re.search(r'\$|%|\d+|amount|cost|fee|limit', question_lower))
    is_definition_question = any(word in question_lower for word in ['what is', 'define', 'meaning'])

    top_matches = []
    for rank, i in enumerate(indices[0]):
        chunk = chunks[i]
        raw_text = chunk["raw_text"]
        text_lower = raw_text.lower()

        text_words = set(re.findall(r"\w+", text_lower))
        common_keywords = question_words & text_words
        insurance_terms = common_keywords & insurance_keywords

        phrase_matches = sum(1 for word in question_words
                           if len(word) > 3 and word in text_lower)
        bigram_matches = sum(3 for bigram in question_bigrams if bigram in text_lower)

        has_numbers = bool(re.search(r'\$|%|\d+', raw_text))
        number_bonus = 0.15 if (has_numbers and question_has_numbers) else 0

        definition_pattern = 'means' in text_lower and bool(re.search(r'\b\w+\s+means\b', text_lower))
        definition_bonus = 0.2 if (definition_pattern and is_definition_question) else 0

        keyword_score = len(common_keywords) + (len(insurance_terms) * 2) + phrase_matches + bigram_matches
        semantic_score = scores[0][rank]

        final_score = (0.7 * semantic_score) + \
                      (0.15 * min(keyword_score / 10.0, 1.0)) + \
                      number_bonus + \
                      definition_bonus

        top_matches.append({
            "text": chunk["text"],
            "raw_text": raw_text,
            "page": chunk["page"],
            "score": final_score
        })

    top_matches = sorted(top_matches, key=lambda x: x["score"], reverse=True)
    return top_matches[:k]

def validate_context_relevance(question, context_chunks, min_relevance=0.25):
    """Enhanced filtering of context chunks based on semantic relevance"""
    question_words = set(re.findall(r'\w+', question.lower()))

    is_definition_question = bool(re.search(r'\bwhat is|\bdefine|\bmeaning|\bdefin[ei]|\bconcept', question.lower()))
    is_coverage_question = bool(re.search(r'\bcover|\bprovide|\binclude|\beligible', question.lower()))
    is_exclusion_question = bool(re.search(r'\bexclude|\bnot cover|\bdeny|\breject', question.lower()))
    is_process_question = bool(re.search(r'\bhow|\bprocess|\bprocedure|\bsteps|\bsubmit', question.lower()))
    is_document_question = bool(re.search(r'\bdocument|\bproof|\bevidence|\breceipt|\bform', question.lower()))
    is_limit_question = bool(re.search(r'\blimit|\bmaximum|\bminimum|\bcap|\bceiling|\bamount', question.lower()))

    relevant_chunks = []
    for chunk in context_chunks:
        chunk_words = set(re.findall(r'\w+', chunk['raw_text'].lower()))
        chunk_text = chunk['raw_text'].lower()

        overlap = len(question_words & chunk_words)
        relevance = overlap / max(len(question_words), 1)

        metadata = chunk.get('metadata', [])

        type_bonus = 0
        if is_definition_question and ('definition' in metadata or bool(re.search(r'\bmeans\b|\bis defined as\b', chunk_text))):
            type_bonus += 0.3
        if is_coverage_question and ('coverage' in metadata or bool(re.search(r'\bcovered\b|\beligible\b', chunk_text))):
            type_bonus += 0.3
        if is_exclusion_question and ('exclusion' in metadata or bool(re.search(r'\bexcluded\b|\bnot covered\b', chunk_text))):
            type_bonus += 0.3
        if is_process_question and bool(re.search(r'\bsteps\b|\bprocess\b|\bprocedure\b', chunk_text)):
            type_bonus += 0.3
        if is_document_question and bool(re.search(r'\bdocument\b|\bproof\b|\bevidence\b|\bform\b', chunk_text)):
            type_bonus += 0.3
        if is_limit_question and ('limit' in metadata or bool(re.search(r'\blimit\b|\bmaximum\b|\bcap\b', chunk_text))):
            type_bonus += 0.3

        final_relevance = relevance + type_bonus

        if final_relevance >= min_relevance or len(relevant_chunks) < 2:
            relevant_chunks.append({
                **chunk,
                "relevance_score": final_relevance
            })

    relevant_chunks = sorted(relevant_chunks, key=lambda x: x.get("relevance_score", 0), reverse=True)
    return relevant_chunks[:7]

def build_insurance_prompt(question, context_chunks):
    """Build optimized prompt for insurance assistant"""
    context = "\n---\n".join([c["text"] for c in context_chunks])
    return f"""
You are a helpful insurance assistant explaining policy details in simple terms. Answer questions about insurance policies in a friendly, conversational tone.

*CRITICAL REQUIREMENTS:*
- Keep responses short and concise (1-2 lines whenever possible)
- Use plain, everyday language instead of technical insurance jargon
- Include only the most essential information like key numbers, conditions, and limits
- Be direct and get straight to the point
- Never add information not found in the context

*RESPONSE STYLE EXAMPLES:*
- "An accident is any sudden, unexpected event caused by something external and visible."
- "Children up to 23 years old can be covered if they depend financially on you."
- "If you're permanently disabled, you'll get 100% of your insured amount."

*CONTEXT FROM POLICY DOCUMENT:*
{context}

*QUESTION:* {question}

Provide a short, friendly answer using simple language. Respond in JSON format:
{{ "answer": "..." }}
"""

async def call_gpt_fast_async(prompt):
    """Async GPT call with caching, batching, and optimized parameters"""
    # Generate cache key for the prompt
    prompt_hash = hashlib.md5(prompt.encode()).hexdigest()

    # Check cache first
    if redis_client and redis_client.connected:
        cached_response = await redis_client.get_api_response_cache(prompt_hash)
        if cached_response:
            return cached_response

    try:
        # Use batch processor for completion requests
        content = await batch_complete_prompt(
            prompt,
            model="gpt-4o-mini",
            temperature=0.1,
            max_tokens=600,
            top_p=0.1,
            response_format={"type": "json_object"}
        )

        if not content:
            return "Error: No response from API"

        try:
            parsed_json = json.loads(content)
            answer = parsed_json.get("answer")
            if answer and answer.strip():
                # Cache the successful response
                if redis_client and redis_client.connected:
                    await redis_client.set_api_response_cache(prompt_hash, answer)
                return answer
        except json.JSONDecodeError:
            if '{"answer":' in content:
                try:
                    start = content.find('{"answer":')
                    end = content.rfind('}') + 1
                    json_str = content[start:end]
                    answer = json.loads(json_str).get("answer", "Not found in document.")
                    # Cache the successful response
                    if redis_client and redis_client.connected:
                        await redis_client.set_api_response_cache(prompt_hash, answer)
                    return answer
                except:
                    pass

            answer_match = re.search(r'"answer"\s*:\s*"([^"]+)"', content)
            if answer_match:
                answer = answer_match.group(1)
                # Cache the successful response
                if redis_client and redis_client.connected:
                    await redis_client.set_api_response_cache(prompt_hash, answer)
                return answer

        return "Not found in document."
    except Exception as e:
        return f"Error: {str(e)}"

async def call_gpt_streaming_async(prompt):
    """Async GPT call with streaming response"""
    try:
        stream = await async_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=600,
            top_p=0.1,
            stream=True
        )

        async for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content

    except Exception as e:
        yield f"Error: {str(e)}"

async def process_question_async(question, index, chunks):
    """Async question processing"""
    try:
        top_chunks = await insurance_specific_retrieve_async(question, index, chunks)
        relevant_chunks = validate_context_relevance(question, top_chunks)
        prompt = build_insurance_prompt(question, relevant_chunks)
        return await call_gpt_fast_async(prompt)
    except Exception as e:
        return f"Error processing question: {str(e)}"

async def process_questions_batch(questions, index, chunks):
    """Process multiple questions concurrently"""
    tasks = [process_question_async(q, index, chunks) for q in questions]
    return await asyncio.gather(*tasks, return_exceptions=True)

# Initialize async components
async def init_app():
    """Initialize async components"""
    await init_redis()
    await init_http_session()
    batch_processor.start()  # Start the batch processor

# Cleanup on shutdown
import atexit
atexit.register(lambda: asyncio.run(cleanup_resources()))

@app.route("/api/v1/hackrx/run", methods=["POST", "OPTIONS"])
def run_submission():
    """Main API endpoint with async processing"""
    if request.method == "OPTIONS":
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
        return '', 204, headers

    return asyncio.run(run_submission_async())

async def run_submission_async():
    """Async implementation of the main endpoint with performance monitoring"""
    start_time = time.time()

    try:
        # Run performance optimizations periodically
        await optimize_for_performance()

        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer ") or auth_header.split(" ")[1] != TEAM_TOKEN:
            return jsonify({"error": "Unauthorized"}), 401

        # Parse JSON with enhanced error handling
        try:
            data = request.get_json(force=True)
            if not data:
                # Fallback parsing for malformed JSON
                raw_data = request.data.decode('utf-8')
                url_pattern = r'documents"?\s*:(?:\s*")?((https?://[^"\s,}]+?)(?:\\"|"|\s|,|}|$))'
                document_match = re.search(url_pattern, raw_data, re.IGNORECASE)

                questions_pattern = r'questions"?\s*:\s*\[(.*?)\]'
                questions_match = re.search(questions_pattern, raw_data, re.DOTALL)

                if document_match and questions_match:
                    document_url = document_match.group(1).split('"')[0].split('\\')[0]
                    questions_text = questions_match.group(1)
                    questions = []
                    for match in re.finditer(r'"([^"]+)"|\'([^\']+)\'', questions_text):
                        if match.group(1):
                            questions.append(match.group(1))
                        else:
                            questions.append(match.group(2))
                    data = {"documents": document_url, "questions": questions}
                else:
                    raise ValueError("Could not parse JSON")

        except Exception as e:
            return jsonify({"error": "Invalid JSON format", "details": str(e)}), 400

        document_url = data.get("documents")
        questions = data.get("questions")

        if not document_url or not questions:
            return jsonify({"error": "Missing 'documents' or 'questions'"}), 400

        # Check cache first
        cache_key = get_cache_key(document_url)
        cached = await load_cache_async(cache_key)

        if cached:
            index, chunks = cached["index"], cached["chunks"]
        else:
            # Extract and process document
            text_by_page = await extract_text_from_url_async(document_url)
            chunk_dicts = generate_smart_chunks(text_by_page)

            if not chunk_dicts:
                return jsonify({"error": "No valid content extracted from document"}), 400

            index, chunks, _ = await embed_chunks_openai_async(chunk_dicts)
            await save_cache_async(cache_key, {"index": index, "chunks": chunks})

        # Process questions concurrently
        answers = await process_questions_batch(questions, index, chunks)

        # Handle any exceptions in results
        processed_answers = []
        for answer in answers:
            if isinstance(answer, Exception):
                processed_answers.append(f"Error: {str(answer)}")
            else:
                processed_answers.append(answer)

        # Record performance metrics
        processing_time = time.time() - start_time
        performance_monitor.record_request(processing_time)

        return jsonify({
            "answers": processed_answers,
            "performance": {
                "processing_time": round(processing_time, 3),
                "cached": bool(cached)
            }
        }), 200

    except Exception as e:
        processing_time = time.time() - start_time
        performance_monitor.record_request(processing_time)
        return jsonify({"error": "Server error", "details": str(e)}), 500

@app.route("/api/v1/hackrx/stream", methods=["POST", "OPTIONS"])
def run_submission_stream():
    """Streaming endpoint for real-time responses"""
    if request.method == "OPTIONS":
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
        return '', 204, headers

    return asyncio.run(run_submission_stream_async())

async def run_submission_stream_async():
    """Async streaming implementation"""
    try:
        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer ") or auth_header.split(" ")[1] != TEAM_TOKEN:
            return jsonify({"error": "Unauthorized"}), 401

        data = request.get_json(force=True)
        document_url = data.get("documents")
        questions = data.get("questions")

        if not document_url or not questions:
            return jsonify({"error": "Missing 'documents' or 'questions'"}), 400

        def generate_stream():
            async def async_generator():
                # Check cache
                cache_key = get_cache_key(document_url)
                cached = await load_cache_async(cache_key)

                if cached:
                    index, chunks = cached["index"], cached["chunks"]
                    yield f"data: {json.dumps({'status': 'cache_hit', 'message': 'Using cached document'})}\n\n"
                else:
                    yield f"data: {json.dumps({'status': 'processing', 'message': 'Extracting document...'})}\n\n"

                    text_by_page = await extract_text_from_url_async(document_url)
                    yield f"data: {json.dumps({'status': 'processing', 'message': 'Generating chunks...'})}\n\n"

                    chunk_dicts = generate_smart_chunks(text_by_page)
                    yield f"data: {json.dumps({'status': 'processing', 'message': 'Creating embeddings...'})}\n\n"

                    index, chunks, _ = await embed_chunks_openai_async(chunk_dicts)
                    await save_cache_async(cache_key, {"index": index, "chunks": chunks})

                # Process questions and stream results
                for i, question in enumerate(questions):
                    yield f"data: {json.dumps({'status': 'processing_question', 'question_index': i, 'question': question})}\n\n"

                    answer = await process_question_async(question, index, chunks)
                    yield f"data: {json.dumps({'status': 'answer', 'question_index': i, 'answer': answer})}\n\n"

                yield f"data: {json.dumps({'status': 'complete'})}\n\n"

            # Run async generator in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async_gen = async_generator()
                while True:
                    try:
                        yield loop.run_until_complete(async_gen.__anext__())
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()

        return Response(
            generate_stream(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            }
        )

    except Exception as e:
        return jsonify({"error": "Server error", "details": str(e)}), 500

@app.route("/health", methods=["GET"])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "version": "async-optimized"}), 200

@app.route("/metrics", methods=["GET"])
def metrics():
    """Performance metrics endpoint with Redis cache stats"""
    return asyncio.run(metrics_async())

async def metrics_async():
    """Async metrics collection"""
    base_metrics = {
        "cache_status": "redis" if (redis_client and redis_client.connected) else "disk_only",
        "http_session": "pooled" if http_session else "not_initialized",
        "optimization_features": [
            "async_io",
            "connection_pooling",
            "redis_caching",
            "batch_processing",
            "streaming_responses",
            "api_response_caching",
            "intelligent_cache_invalidation"
        ]
    }

    # Add Redis cache statistics if available
    if redis_client and redis_client.connected:
        cache_info = await redis_client.get_cache_info()
        base_metrics["redis_cache"] = cache_info

    # Add batch processing statistics
    base_metrics["batch_processing"] = batch_processor.get_stats()

    # Add comprehensive performance statistics
    base_metrics["comprehensive_stats"] = get_comprehensive_stats()

    return jsonify(base_metrics), 200

@app.route("/performance", methods=["GET"])
def performance_dashboard():
    """Detailed performance dashboard"""
    return asyncio.run(performance_dashboard_async())

async def performance_dashboard_async():
    """Async performance dashboard with detailed metrics"""
    await ensure_initialized()

    # Get all performance data
    stats = get_comprehensive_stats()

    # Add additional runtime information
    stats["runtime_info"] = {
        "python_version": sys.version,
        "event_loop": "uvloop" if "uvloop" in str(asyncio.get_event_loop()) else "asyncio",
        "server_type": "Flask + asyncio",
        "optimization_level": "maximum"
    }

    # Add recommendations based on current performance
    recommendations = []

    memory_stats = stats["memory_stats"]
    if memory_stats["memory_percent"] > 80:
        recommendations.append("High memory usage detected. Consider increasing server memory or reducing cache sizes.")

    perf_stats = stats["performance_stats"]
    if perf_stats["cache_hit_rate"] < 0.5:
        recommendations.append("Low cache hit rate. Consider increasing cache TTL or warming up caches.")

    if perf_stats["average_processing_time"] > 5.0:
        recommendations.append("High average processing time. Consider optimizing document chunking or using GPU acceleration.")

    stats["recommendations"] = recommendations

    return jsonify(stats), 200

# Initialize app on startup
@app.before_first_request
def startup():
    """Initialize async components on first request"""
    asyncio.run(init_app())

if __name__ == "__main__":
    # Initialize async components
    asyncio.run(init_app())

    # Run with optimized settings
    app.run(
        host="0.0.0.0",
        port=int(os.environ.get("PORT", 5000)),
        threaded=True,  # Enable threading for better concurrency
        debug=False     # Disable debug for production performance
    )
