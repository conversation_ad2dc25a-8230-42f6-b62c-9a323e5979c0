"""
ASGI Application for High-Performance Concurrent Request Handling
Uses Quart (async Flask) for true async request processing
"""

import asyncio
import os
import json
import re
import hashlib
from typing import Dict, List, Any
from quart import Quart, request, jsonify, Response
from quart_cors import cors
import uvloop

# Import our optimized modules
from redis_config import redis_manager, init_redis_cache, cleanup_redis_cache
from batch_processor import batch_processor, batch_embed_texts, batch_complete_prompt

# Import processing functions from the async app
import sys
sys.path.append('.')

# Set uvloop for maximum performance
asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

# Create Quart app (async Flask alternative)
app = Quart(__name__)
app = cors(app, allow_origin="*")

# Configuration
TEAM_TOKEN = "d1b791fa0ef5092d9cd051b2b09df2473d1e2ea07e09fe6c61abb5722dfbc7d3"
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

# Import processing functions
from app_async import (
    extract_text_from_url_async,
    generate_smart_chunks,
    embed_chunks_openai_async,
    insurance_specific_retrieve_async,
    validate_context_relevance,
    build_insurance_prompt,
    call_gpt_fast_async,
    get_cache_key,
    load_cache_async,
    save_cache_async,
    init_http_session,
    cleanup_resources
)

# Global state
initialized = False

async def ensure_initialized():
    """Ensure all components are initialized"""
    global initialized
    if not initialized:
        await init_redis_cache(REDIS_URL)
        await init_http_session()
        batch_processor.start()
        initialized = True

@app.before_serving
async def startup():
    """Initialize components on startup"""
    await ensure_initialized()
    print("🚀 ASGI app initialized with high-performance optimizations")

@app.after_serving
async def shutdown():
    """Cleanup on shutdown"""
    await cleanup_resources()
    await batch_processor.stop()
    print("✅ ASGI app shutdown complete")

@app.route("/api/v1/hackrx/run", methods=["POST", "OPTIONS"])
async def run_submission():
    """High-performance async endpoint"""
    if request.method == "OPTIONS":
        return "", 204, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    
    await ensure_initialized()
    
    try:
        # Authentication
        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer ") or auth_header.split(" ")[1] != TEAM_TOKEN:
            return jsonify({"error": "Unauthorized"}), 401
        
        # Parse request data
        try:
            data = await request.get_json(force=True)
            if not data:
                # Enhanced JSON parsing for malformed requests
                raw_data = (await request.get_data()).decode('utf-8')
                data = await parse_malformed_json(raw_data)
        except Exception as e:
            return jsonify({"error": "Invalid JSON format", "details": str(e)}), 400
            
        document_url = data.get("documents")
        questions = data.get("questions")

        if not document_url or not questions:
            return jsonify({"error": "Missing 'documents' or 'questions'"}), 400

        # Process with caching
        cache_key = get_cache_key(document_url)
        cached = await load_cache_async(cache_key)

        if cached:
            index, chunks = cached["index"], cached["chunks"]
        else:
            # Extract and process document
            text_by_page = await extract_text_from_url_async(document_url)
            chunk_dicts = generate_smart_chunks(text_by_page)
            
            if not chunk_dicts:
                return jsonify({"error": "No valid content extracted from document"}), 400
                
            index, chunks, _ = await embed_chunks_openai_async(chunk_dicts)
            await save_cache_async(cache_key, {"index": index, "chunks": chunks})

        # Process questions concurrently with advanced batching
        answers = await process_questions_concurrent(questions, index, chunks)
        
        return jsonify({"answers": answers}), 200

    except Exception as e:
        return jsonify({"error": "Server error", "details": str(e)}), 500

@app.route("/api/v1/hackrx/stream", methods=["POST", "OPTIONS"])
async def run_submission_stream():
    """Streaming endpoint with Server-Sent Events"""
    if request.method == "OPTIONS":
        return "", 204, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    
    await ensure_initialized()
    
    try:
        # Authentication
        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer ") or auth_header.split(" ")[1] != TEAM_TOKEN:
            return jsonify({"error": "Unauthorized"}), 401
        
        data = await request.get_json(force=True)
        document_url = data.get("documents")
        questions = data.get("questions")

        if not document_url or not questions:
            return jsonify({"error": "Missing 'documents' or 'questions'"}), 400

        async def generate_stream():
            """Generate streaming response"""
            try:
                # Check cache
                cache_key = get_cache_key(document_url)
                cached = await load_cache_async(cache_key)

                if cached:
                    index, chunks = cached["index"], cached["chunks"]
                    yield f"data: {json.dumps({'status': 'cache_hit', 'message': 'Using cached document'})}\n\n"
                else:
                    yield f"data: {json.dumps({'status': 'processing', 'message': 'Extracting document...'})}\n\n"
                    
                    text_by_page = await extract_text_from_url_async(document_url)
                    yield f"data: {json.dumps({'status': 'processing', 'message': 'Generating chunks...'})}\n\n"
                    
                    chunk_dicts = generate_smart_chunks(text_by_page)
                    yield f"data: {json.dumps({'status': 'processing', 'message': 'Creating embeddings...'})}\n\n"
                    
                    index, chunks, _ = await embed_chunks_openai_async(chunk_dicts)
                    await save_cache_async(cache_key, {"index": index, "chunks": chunks})

                # Process questions with real-time updates
                for i, question in enumerate(questions):
                    yield f"data: {json.dumps({'status': 'processing_question', 'question_index': i, 'question': question})}\n\n"
                    
                    answer = await process_single_question(question, index, chunks)
                    yield f"data: {json.dumps({'status': 'answer', 'question_index': i, 'answer': answer})}\n\n"

                yield f"data: {json.dumps({'status': 'complete'})}\n\n"

            except Exception as e:
                yield f"data: {json.dumps({'status': 'error', 'error': str(e)})}\n\n"

        return Response(
            generate_stream(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'X-Accel-Buffering': 'no'  # Disable nginx buffering
            }
        )

    except Exception as e:
        return jsonify({"error": "Server error", "details": str(e)}), 500

@app.route("/health", methods=["GET"])
async def health_check():
    """Health check endpoint"""
    await ensure_initialized()
    return jsonify({
        "status": "healthy", 
        "version": "asgi-optimized",
        "performance_mode": "maximum"
    }), 200

@app.route("/metrics", methods=["GET"])
async def metrics():
    """Comprehensive performance metrics"""
    await ensure_initialized()
    
    base_metrics = {
        "server_type": "ASGI (Quart)",
        "event_loop": "uvloop",
        "optimization_features": [
            "async_io",
            "connection_pooling", 
            "redis_caching",
            "batch_processing",
            "streaming_responses",
            "api_response_caching",
            "intelligent_cache_invalidation",
            "concurrent_request_handling",
            "uvloop_performance"
        ]
    }
    
    # Add Redis cache statistics
    if redis_manager.connected:
        cache_info = await redis_manager.get_cache_info()
        base_metrics["redis_cache"] = cache_info
    
    # Add batch processing statistics
    base_metrics["batch_processing"] = batch_processor.get_stats()
    
    return jsonify(base_metrics), 200

async def parse_malformed_json(raw_data: str) -> Dict[str, Any]:
    """Parse malformed JSON with fallback strategies"""
    try:
        return json.loads(raw_data)
    except json.JSONDecodeError:
        # Regex-based parsing for malformed JSON
        url_pattern = r'documents"?\s*:(?:\s*")?((https?://[^"\s,}]+?)(?:\\"|"|\s|,|}|$))'
        document_match = re.search(url_pattern, raw_data, re.IGNORECASE)
        
        questions_pattern = r'questions"?\s*:\s*\[(.*?)\]'
        questions_match = re.search(questions_pattern, raw_data, re.DOTALL)
        
        if document_match and questions_match:
            document_url = document_match.group(1).split('"')[0].split('\\')[0]
            questions_text = questions_match.group(1)
            questions = []
            for match in re.finditer(r'"([^"]+)"|\'([^\']+)\'', questions_text):
                if match.group(1):
                    questions.append(match.group(1))
                else:
                    questions.append(match.group(2))
            return {"documents": document_url, "questions": questions}
        else:
            raise ValueError("Could not parse malformed JSON")

async def process_questions_concurrent(questions: List[str], index, chunks) -> List[str]:
    """Process multiple questions with optimal concurrency"""
    # Use asyncio.gather for maximum concurrency
    tasks = [process_single_question(q, index, chunks) for q in questions]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Handle any exceptions
    processed_results = []
    for result in results:
        if isinstance(result, Exception):
            processed_results.append(f"Error: {str(result)}")
        else:
            processed_results.append(result)
    
    return processed_results

async def process_single_question(question: str, index, chunks) -> str:
    """Process a single question with full optimization"""
    try:
        top_chunks = await insurance_specific_retrieve_async(question, index, chunks)
        relevant_chunks = validate_context_relevance(question, top_chunks)
        prompt = build_insurance_prompt(question, relevant_chunks)
        return await call_gpt_fast_async(prompt)
    except Exception as e:
        return f"Error processing question: {str(e)}"

if __name__ == "__main__":
    import hypercorn.asyncio
    from hypercorn.config import Config
    
    # Hypercorn configuration for maximum performance
    config = Config()
    config.bind = [f"0.0.0.0:{int(os.environ.get('PORT', 5000))}"]
    config.workers = min(4, (os.cpu_count() or 1) + 1)  # Optimal worker count
    config.worker_class = "asyncio"
    config.max_requests = 10000  # Restart workers after 10k requests
    config.max_requests_jitter = 1000
    config.keepalive_timeout = 65
    config.graceful_timeout = 30
    config.access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'
    
    print("🚀 Starting high-performance ASGI server with Hypercorn...")
    asyncio.run(hypercorn.asyncio.serve(app, config))
