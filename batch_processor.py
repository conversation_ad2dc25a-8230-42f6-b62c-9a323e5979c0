"""
Advanced Batch Processing System for Insurance Assistant
Optimizes API calls and processing through intelligent batching and queuing
"""

import asyncio
import time
from typing import List, Dict, Any, Callable, Optional, Tuple
from dataclasses import dataclass, field
from collections import defaultdict
import hashlib
import json
from openai import AsyncOpenAI
import numpy as np

@dataclass
class BatchRequest:
    """Represents a single request in a batch"""
    id: str
    data: Any
    callback: Optional[Callable] = None
    priority: int = 0  # Higher number = higher priority
    created_at: float = field(default_factory=time.time)
    timeout: float = 30.0  # Request timeout in seconds

@dataclass
class BatchResult:
    """Represents the result of a batch request"""
    request_id: str
    result: Any
    error: Optional[str] = None
    processing_time: float = 0.0

class BatchProcessor:
    """Advanced batch processor with intelligent queuing and optimization"""
    
    def __init__(self, 
                 batch_size: int = 100,
                 max_wait_time: float = 2.0,
                 max_concurrent_batches: int = 5):
        self.batch_size = batch_size
        self.max_wait_time = max_wait_time
        self.max_concurrent_batches = max_concurrent_batches
        
        # Request queues by type
        self.embedding_queue: List[BatchRequest] = []
        self.completion_queue: List[BatchRequest] = []
        
        # Processing state
        self.processing = False
        self.active_batches = 0
        self.stats = {
            'total_requests': 0,
            'total_batches': 0,
            'avg_batch_size': 0,
            'avg_processing_time': 0,
            'cache_hits': 0,
            'api_calls': 0
        }
        
        # Results storage
        self.results: Dict[str, BatchResult] = {}
        self.pending_requests: Dict[str, asyncio.Future] = {}
        
        # Start background processor
        self.processor_task = None
    
    def start(self):
        """Start the batch processor"""
        if not self.processor_task or self.processor_task.done():
            self.processor_task = asyncio.create_task(self._process_batches())
    
    async def stop(self):
        """Stop the batch processor"""
        if self.processor_task:
            self.processor_task.cancel()
            try:
                await self.processor_task
            except asyncio.CancelledError:
                pass
    
    async def add_embedding_request(self, texts: List[str], model: str = "text-embedding-3-large") -> List[List[float]]:
        """Add embedding request to batch queue"""
        request_id = hashlib.md5(f"{texts}_{model}_{time.time()}".encode()).hexdigest()
        
        request = BatchRequest(
            id=request_id,
            data={'texts': texts, 'model': model, 'type': 'embedding'}
        )
        
        # Create future for result
        future = asyncio.Future()
        self.pending_requests[request_id] = future
        
        # Add to queue
        self.embedding_queue.append(request)
        self.stats['total_requests'] += 1
        
        # Start processor if not running
        self.start()
        
        # Wait for result
        try:
            result = await asyncio.wait_for(future, timeout=request.timeout)
            return result.result if not result.error else None
        except asyncio.TimeoutError:
            # Clean up on timeout
            self.pending_requests.pop(request_id, None)
            raise TimeoutError(f"Embedding request {request_id} timed out")
    
    async def add_completion_request(self, prompt: str, model: str = "gpt-4o-mini", **kwargs) -> str:
        """Add completion request to batch queue"""
        request_id = hashlib.md5(f"{prompt}_{model}_{time.time()}".encode()).hexdigest()
        
        request = BatchRequest(
            id=request_id,
            data={'prompt': prompt, 'model': model, 'type': 'completion', 'kwargs': kwargs}
        )
        
        # Create future for result
        future = asyncio.Future()
        self.pending_requests[request_id] = future
        
        # Add to queue
        self.completion_queue.append(request)
        self.stats['total_requests'] += 1
        
        # Start processor if not running
        self.start()
        
        # Wait for result
        try:
            result = await asyncio.wait_for(future, timeout=request.timeout)
            return result.result if not result.error else None
        except asyncio.TimeoutError:
            # Clean up on timeout
            self.pending_requests.pop(request_id, None)
            raise TimeoutError(f"Completion request {request_id} timed out")
    
    async def _process_batches(self):
        """Background task to process batches"""
        while True:
            try:
                # Process embedding batches
                if self.embedding_queue and self.active_batches < self.max_concurrent_batches:
                    await self._process_embedding_batch()
                
                # Process completion batches (these are typically individual requests)
                if self.completion_queue and self.active_batches < self.max_concurrent_batches:
                    await self._process_completion_batch()
                
                # Wait before next iteration
                await asyncio.sleep(0.1)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in batch processor: {e}")
                await asyncio.sleep(1)
    
    async def _process_embedding_batch(self):
        """Process a batch of embedding requests"""
        if not self.embedding_queue:
            return
        
        # Collect batch
        batch_requests = []
        batch_texts = []
        
        # Group by model for efficiency
        model_groups = defaultdict(list)
        
        while (len(batch_requests) < self.batch_size and 
               self.embedding_queue and 
               (not batch_requests or time.time() - batch_requests[0].created_at < self.max_wait_time)):
            
            request = self.embedding_queue.pop(0)
            batch_requests.append(request)
            
            model = request.data['model']
            model_groups[model].extend([(request.id, text) for text in request.data['texts']])
        
        if not batch_requests:
            return
        
        self.active_batches += 1
        start_time = time.time()
        
        try:
            # Process each model group
            all_results = {}
            
            for model, text_pairs in model_groups.items():
                texts = [text for _, text in text_pairs]
                request_ids = [req_id for req_id, _ in text_pairs]
                
                # Make API call
                client = AsyncOpenAI()
                response = await client.embeddings.create(
                    model=model,
                    input=texts
                )
                
                # Map results back to request IDs
                embeddings = [d.embedding for d in response.data]
                
                # Group embeddings by original request
                request_embeddings = defaultdict(list)
                text_index = 0
                
                for request in batch_requests:
                    if request.data['model'] == model:
                        num_texts = len(request.data['texts'])
                        request_embeddings[request.id] = embeddings[text_index:text_index + num_texts]
                        text_index += num_texts
                
                all_results.update(request_embeddings)
            
            # Send results to waiting futures
            processing_time = time.time() - start_time
            
            for request in batch_requests:
                result = BatchResult(
                    request_id=request.id,
                    result=all_results.get(request.id, []),
                    processing_time=processing_time
                )
                
                future = self.pending_requests.pop(request.id, None)
                if future and not future.done():
                    future.set_result(result)
            
            # Update stats
            self.stats['total_batches'] += 1
            self.stats['avg_batch_size'] = (
                (self.stats['avg_batch_size'] * (self.stats['total_batches'] - 1) + len(batch_requests)) 
                / self.stats['total_batches']
            )
            self.stats['avg_processing_time'] = (
                (self.stats['avg_processing_time'] * (self.stats['total_batches'] - 1) + processing_time) 
                / self.stats['total_batches']
            )
            self.stats['api_calls'] += len(model_groups)
            
        except Exception as e:
            # Handle errors
            for request in batch_requests:
                result = BatchResult(
                    request_id=request.id,
                    result=None,
                    error=str(e)
                )
                
                future = self.pending_requests.pop(request.id, None)
                if future and not future.done():
                    future.set_result(result)
        
        finally:
            self.active_batches -= 1
    
    async def _process_completion_batch(self):
        """Process completion requests (typically individual)"""
        if not self.completion_queue:
            return
        
        # For now, process completions individually as they're harder to batch
        # In the future, could implement prompt batching for similar requests
        
        request = self.completion_queue.pop(0)
        self.active_batches += 1
        start_time = time.time()
        
        try:
            client = AsyncOpenAI()
            response = await client.chat.completions.create(
                model=request.data['model'],
                messages=[{"role": "user", "content": request.data['prompt']}],
                **request.data.get('kwargs', {})
            )
            
            result_text = response.choices[0].message.content
            processing_time = time.time() - start_time
            
            result = BatchResult(
                request_id=request.id,
                result=result_text,
                processing_time=processing_time
            )
            
            future = self.pending_requests.pop(request.id, None)
            if future and not future.done():
                future.set_result(result)
            
            # Update stats
            self.stats['api_calls'] += 1
            
        except Exception as e:
            result = BatchResult(
                request_id=request.id,
                result=None,
                error=str(e)
            )
            
            future = self.pending_requests.pop(request.id, None)
            if future and not future.done():
                future.set_result(result)
        
        finally:
            self.active_batches -= 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return {
            **self.stats,
            'queue_sizes': {
                'embedding': len(self.embedding_queue),
                'completion': len(self.completion_queue)
            },
            'active_batches': self.active_batches,
            'pending_requests': len(self.pending_requests)
        }

# Global batch processor instance
batch_processor = BatchProcessor()

# Convenience functions
async def batch_embed_texts(texts: List[str], model: str = "text-embedding-3-large") -> List[List[float]]:
    """Batch embedding function"""
    return await batch_processor.add_embedding_request(texts, model)

async def batch_complete_prompt(prompt: str, model: str = "gpt-4o-mini", **kwargs) -> str:
    """Batch completion function"""
    return await batch_processor.add_completion_request(prompt, model, **kwargs)

# Export for use in main application
__all__ = ['BatchProcessor', 'batch_processor', 'batch_embed_texts', 'batch_complete_prompt']
