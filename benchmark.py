#!/usr/bin/env python3
"""
Performance Benchmark Script for Insurance Assistant
Compares original vs optimized implementation performance
"""

import asyncio
import aiohttp
import time
import json
import statistics
from typing import List, Dict, Any
import argparse
import sys

# Test configuration
TEST_DOCUMENT_URL = "https://www.icicilombard.com/docs/default-source/policy-wordings/motor-insurance/private-car/icicilombard-motor-private-car-policy-wording.pdf"
TEST_QUESTIONS = [
    "What is covered under this policy?",
    "What are the exclusions?",
    "How do I file a claim?",
    "What is the deductible amount?",
    "What documents are required for claims?"
]

TEAM_TOKEN = "d1b791fa0ef5092d9cd051b2b09df2473d1e2ea07e09fe6c61abb5722dfbc7d3"

class PerformanceBenchmark:
    """Performance benchmarking tool"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.results = {}
        
    async def benchmark_endpoint(self, endpoint: str, num_requests: int = 5) -> Dict[str, Any]:
        """Benchmark a specific endpoint"""
        print(f"🔍 Benchmarking {endpoint} with {num_requests} requests...")
        
        url = f"{self.base_url}{endpoint}"
        headers = {
            "Authorization": f"Bearer {TEAM_TOKEN}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "documents": TEST_DOCUMENT_URL,
            "questions": TEST_QUESTIONS
        }
        
        response_times = []
        success_count = 0
        errors = []
        
        async with aiohttp.ClientSession() as session:
            for i in range(num_requests):
                print(f"  Request {i+1}/{num_requests}...", end=" ")
                
                start_time = time.time()
                try:
                    async with session.post(url, headers=headers, json=payload) as response:
                        if response.status == 200:
                            data = await response.json()
                            end_time = time.time()
                            response_time = end_time - start_time
                            response_times.append(response_time)
                            success_count += 1
                            print(f"✅ {response_time:.2f}s")
                            
                            # Check if response includes performance data
                            if 'performance' in data:
                                perf_data = data['performance']
                                print(f"    Processing time: {perf_data.get('processing_time', 'N/A')}s")
                                print(f"    Cached: {perf_data.get('cached', 'N/A')}")
                        else:
                            error_msg = f"HTTP {response.status}"
                            errors.append(error_msg)
                            print(f"❌ {error_msg}")
                            
                except Exception as e:
                    errors.append(str(e))
                    print(f"❌ {str(e)}")
                
                # Small delay between requests
                if i < num_requests - 1:
                    await asyncio.sleep(1)
        
        # Calculate statistics
        if response_times:
            stats = {
                "endpoint": endpoint,
                "total_requests": num_requests,
                "successful_requests": success_count,
                "failed_requests": len(errors),
                "success_rate": (success_count / num_requests) * 100,
                "avg_response_time": statistics.mean(response_times),
                "min_response_time": min(response_times),
                "max_response_time": max(response_times),
                "median_response_time": statistics.median(response_times),
                "std_dev": statistics.stdev(response_times) if len(response_times) > 1 else 0,
                "errors": errors
            }
        else:
            stats = {
                "endpoint": endpoint,
                "total_requests": num_requests,
                "successful_requests": 0,
                "failed_requests": num_requests,
                "success_rate": 0,
                "errors": errors
            }
        
        return stats
    
    async def benchmark_streaming(self, num_requests: int = 3) -> Dict[str, Any]:
        """Benchmark streaming endpoint"""
        print(f"🔍 Benchmarking streaming endpoint with {num_requests} requests...")
        
        url = f"{self.base_url}/api/v1/hackrx/stream"
        headers = {
            "Authorization": f"Bearer {TEAM_TOKEN}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "documents": TEST_DOCUMENT_URL,
            "questions": TEST_QUESTIONS
        }
        
        response_times = []
        first_response_times = []
        success_count = 0
        errors = []
        
        async with aiohttp.ClientSession() as session:
            for i in range(num_requests):
                print(f"  Streaming request {i+1}/{num_requests}...", end=" ")
                
                start_time = time.time()
                first_response_time = None
                
                try:
                    async with session.post(url, headers=headers, json=payload) as response:
                        if response.status == 200:
                            async for line in response.content:
                                if first_response_time is None:
                                    first_response_time = time.time() - start_time
                                
                                line_str = line.decode('utf-8').strip()
                                if line_str.startswith('data: '):
                                    try:
                                        data = json.loads(line_str[6:])
                                        if data.get('status') == 'complete':
                                            break
                                    except json.JSONDecodeError:
                                        pass
                            
                            end_time = time.time()
                            total_time = end_time - start_time
                            response_times.append(total_time)
                            if first_response_time:
                                first_response_times.append(first_response_time)
                            success_count += 1
                            print(f"✅ {total_time:.2f}s (first: {first_response_time:.2f}s)")
                        else:
                            error_msg = f"HTTP {response.status}"
                            errors.append(error_msg)
                            print(f"❌ {error_msg}")
                            
                except Exception as e:
                    errors.append(str(e))
                    print(f"❌ {str(e)}")
                
                # Small delay between requests
                if i < num_requests - 1:
                    await asyncio.sleep(2)
        
        # Calculate statistics
        if response_times:
            stats = {
                "endpoint": "/api/v1/hackrx/stream",
                "total_requests": num_requests,
                "successful_requests": success_count,
                "failed_requests": len(errors),
                "success_rate": (success_count / num_requests) * 100,
                "avg_total_time": statistics.mean(response_times),
                "avg_first_response_time": statistics.mean(first_response_times) if first_response_times else 0,
                "min_total_time": min(response_times),
                "max_total_time": max(response_times),
                "errors": errors
            }
        else:
            stats = {
                "endpoint": "/api/v1/hackrx/stream",
                "total_requests": num_requests,
                "successful_requests": 0,
                "failed_requests": num_requests,
                "success_rate": 0,
                "errors": errors
            }
        
        return stats
    
    async def get_server_metrics(self) -> Dict[str, Any]:
        """Get server performance metrics"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/metrics") as response:
                    if response.status == 200:
                        return await response.json()
        except Exception as e:
            print(f"⚠️  Could not fetch server metrics: {e}")
        
        return {}
    
    def print_results(self, results: List[Dict[str, Any]]):
        """Print benchmark results in a formatted way"""
        print("\n" + "="*80)
        print("📊 BENCHMARK RESULTS")
        print("="*80)
        
        for result in results:
            endpoint = result.get('endpoint', 'Unknown')
            print(f"\n🎯 Endpoint: {endpoint}")
            print("-" * 50)
            
            if result.get('successful_requests', 0) > 0:
                print(f"✅ Success Rate: {result['success_rate']:.1f}%")
                print(f"⏱️  Average Response Time: {result.get('avg_response_time', result.get('avg_total_time', 0)):.2f}s")
                print(f"🚀 Min Response Time: {result.get('min_response_time', result.get('min_total_time', 0)):.2f}s")
                print(f"🐌 Max Response Time: {result.get('max_response_time', result.get('max_total_time', 0)):.2f}s")
                
                if 'median_response_time' in result:
                    print(f"📊 Median Response Time: {result['median_response_time']:.2f}s")
                    print(f"📈 Standard Deviation: {result['std_dev']:.2f}s")
                
                if 'avg_first_response_time' in result:
                    print(f"⚡ Average First Response: {result['avg_first_response_time']:.2f}s")
            else:
                print("❌ All requests failed")
            
            if result.get('errors'):
                print(f"🚨 Errors: {len(result['errors'])}")
                for error in result['errors'][:3]:  # Show first 3 errors
                    print(f"   - {error}")
    
    async def run_full_benchmark(self, num_requests: int = 5):
        """Run complete benchmark suite"""
        print("🚀 Starting Performance Benchmark")
        print(f"📍 Server: {self.base_url}")
        print(f"📄 Test Document: {TEST_DOCUMENT_URL}")
        print(f"❓ Questions: {len(TEST_QUESTIONS)}")
        print(f"🔄 Requests per endpoint: {num_requests}")
        print()
        
        # Get initial server metrics
        print("📊 Fetching server metrics...")
        initial_metrics = await self.get_server_metrics()
        if initial_metrics:
            print(f"   Cache Status: {initial_metrics.get('cache_status', 'Unknown')}")
            print(f"   Server Type: {initial_metrics.get('server_type', 'Unknown')}")
            if 'optimization_features' in initial_metrics:
                print(f"   Optimizations: {len(initial_metrics['optimization_features'])} active")
        
        results = []
        
        # Benchmark standard endpoint
        standard_result = await self.benchmark_endpoint("/api/v1/hackrx/run", num_requests)
        results.append(standard_result)
        
        # Benchmark streaming endpoint
        streaming_result = await self.benchmark_streaming(max(3, num_requests // 2))
        results.append(streaming_result)
        
        # Print results
        self.print_results(results)
        
        # Get final server metrics
        print("\n📊 Final Server Metrics:")
        final_metrics = await self.get_server_metrics()
        if final_metrics:
            if 'redis_cache' in final_metrics:
                cache_stats = final_metrics['redis_cache']
                print(f"   Cache Hit Rate: {cache_stats.get('performance_stats', {}).get('hit_rate_percent', 'N/A')}%")
            
            if 'batch_processing' in final_metrics:
                batch_stats = final_metrics['batch_processing']
                print(f"   Avg Batch Size: {batch_stats.get('avg_batch_size', 'N/A')}")
                print(f"   Total API Calls: {batch_stats.get('api_calls', 'N/A')}")
        
        return results

async def main():
    """Main benchmark function"""
    parser = argparse.ArgumentParser(description="Benchmark Insurance Assistant performance")
    parser.add_argument("--url", default="http://localhost:5000", 
                       help="Base URL of the server to benchmark")
    parser.add_argument("--requests", type=int, default=5,
                       help="Number of requests per endpoint")
    parser.add_argument("--endpoint", choices=["standard", "streaming", "both"], 
                       default="both", help="Which endpoint(s) to benchmark")
    
    args = parser.parse_args()
    
    benchmark = PerformanceBenchmark(args.url)
    
    try:
        if args.endpoint == "both":
            await benchmark.run_full_benchmark(args.requests)
        elif args.endpoint == "standard":
            result = await benchmark.benchmark_endpoint("/api/v1/hackrx/run", args.requests)
            benchmark.print_results([result])
        elif args.endpoint == "streaming":
            result = await benchmark.benchmark_streaming(args.requests)
            benchmark.print_results([result])
            
    except KeyboardInterrupt:
        print("\n👋 Benchmark interrupted by user")
    except Exception as e:
        print(f"\n❌ Benchmark error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
