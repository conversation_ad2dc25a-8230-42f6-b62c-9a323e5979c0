"""
Advanced Performance Optimizations for Insurance Assistant
Includes memory optimization, precomputed embeddings, and speed enhancements
"""

import gc
import psutil
import numpy as np
import faiss
import pickle
import hashlib
import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
from functools import lru_cache
from dataclasses import dataclass
import weakref
import mmap
import os

@dataclass
class MemoryStats:
    """Memory usage statistics"""
    total_memory: float
    available_memory: float
    used_memory: float
    memory_percent: float
    cache_size: int
    embeddings_cache_size: int

class MemoryOptimizer:
    """Advanced memory management and optimization"""
    
    def __init__(self, max_memory_percent: float = 80.0):
        self.max_memory_percent = max_memory_percent
        self.embedding_cache = weakref.WeakValueDictionary()
        self.chunk_cache = {}
        self.max_cache_size = 1000
        
    def get_memory_stats(self) -> MemoryStats:
        """Get current memory statistics"""
        memory = psutil.virtual_memory()
        return MemoryStats(
            total_memory=memory.total / (1024**3),  # GB
            available_memory=memory.available / (1024**3),  # GB
            used_memory=memory.used / (1024**3),  # GB
            memory_percent=memory.percent,
            cache_size=len(self.chunk_cache),
            embeddings_cache_size=len(self.embedding_cache)
        )
    
    def should_cleanup_memory(self) -> bool:
        """Check if memory cleanup is needed"""
        memory = psutil.virtual_memory()
        return memory.percent > self.max_memory_percent
    
    def cleanup_memory(self):
        """Perform aggressive memory cleanup"""
        # Clear caches
        if len(self.chunk_cache) > self.max_cache_size // 2:
            # Remove oldest half of cache entries
            items = list(self.chunk_cache.items())
            items.sort(key=lambda x: x[1].get('last_accessed', 0))
            for key, _ in items[:len(items)//2]:
                del self.chunk_cache[key]
        
        # Force garbage collection
        gc.collect()
        
        print(f"Memory cleanup completed. Current usage: {psutil.virtual_memory().percent:.1f}%")
    
    def optimize_numpy_arrays(self, arrays: List[np.ndarray]) -> List[np.ndarray]:
        """Optimize numpy arrays for memory efficiency"""
        optimized = []
        for arr in arrays:
            # Convert to most efficient dtype
            if arr.dtype == np.float64:
                arr = arr.astype(np.float32)
            
            # Ensure C-contiguous for better performance
            if not arr.flags.c_contiguous:
                arr = np.ascontiguousarray(arr)
            
            optimized.append(arr)
        
        return optimized

class EmbeddingCache:
    """High-performance embedding cache with memory mapping"""
    
    def __init__(self, cache_dir: str = "embedding_cache"):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        self.memory_cache = {}
        self.max_memory_items = 100
        
    def _get_cache_path(self, key: str) -> str:
        """Get cache file path for a key"""
        return os.path.join(self.cache_dir, f"{key}.npy")
    
    def _get_metadata_path(self, key: str) -> str:
        """Get metadata file path for a key"""
        return os.path.join(self.cache_dir, f"{key}.meta")
    
    async def get_embeddings(self, key: str) -> Optional[np.ndarray]:
        """Get embeddings from cache"""
        # Check memory cache first
        if key in self.memory_cache:
            return self.memory_cache[key]
        
        # Check disk cache
        cache_path = self._get_cache_path(key)
        if os.path.exists(cache_path):
            try:
                # Use memory mapping for large files
                embeddings = np.load(cache_path, mmap_mode='r')
                
                # Store in memory cache if small enough
                if len(self.memory_cache) < self.max_memory_items:
                    self.memory_cache[key] = embeddings.copy()
                
                return embeddings
            except Exception as e:
                print(f"Error loading embeddings from cache: {e}")
        
        return None
    
    async def set_embeddings(self, key: str, embeddings: np.ndarray, metadata: Dict = None):
        """Store embeddings in cache"""
        try:
            # Save to disk
            cache_path = self._get_cache_path(key)
            np.save(cache_path, embeddings)
            
            # Save metadata
            if metadata:
                metadata_path = self._get_metadata_path(key)
                with open(metadata_path, 'wb') as f:
                    pickle.dump(metadata, f)
            
            # Store in memory cache if space available
            if len(self.memory_cache) < self.max_memory_items:
                self.memory_cache[key] = embeddings
            
        except Exception as e:
            print(f"Error saving embeddings to cache: {e}")
    
    def cleanup_old_cache(self, max_age_days: int = 7):
        """Remove old cache files"""
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 3600
        
        for filename in os.listdir(self.cache_dir):
            file_path = os.path.join(self.cache_dir, filename)
            if os.path.isfile(file_path):
                file_age = current_time - os.path.getmtime(file_path)
                if file_age > max_age_seconds:
                    try:
                        os.remove(file_path)
                    except Exception:
                        pass

class FAISSOptimizer:
    """FAISS index optimization for better performance"""
    
    @staticmethod
    def optimize_index(embeddings: np.ndarray, use_gpu: bool = False) -> faiss.Index:
        """Create optimized FAISS index"""
        dimension = embeddings.shape[1]
        
        # Choose optimal index type based on data size
        if len(embeddings) < 1000:
            # Small dataset: use flat index
            index = faiss.IndexFlatIP(dimension)
        elif len(embeddings) < 10000:
            # Medium dataset: use IVF with clustering
            nlist = min(100, len(embeddings) // 10)
            quantizer = faiss.IndexFlatIP(dimension)
            index = faiss.IndexIVFFlat(quantizer, dimension, nlist)
        else:
            # Large dataset: use IVF with PQ compression
            nlist = min(1000, len(embeddings) // 100)
            m = 8  # Number of subquantizers
            quantizer = faiss.IndexFlatIP(dimension)
            index = faiss.IndexIVFPQ(quantizer, dimension, nlist, m, 8)
        
        # Train index if needed
        if hasattr(index, 'train'):
            index.train(embeddings)
        
        # Add embeddings
        index.add(embeddings)
        
        # Move to GPU if available and requested
        if use_gpu and faiss.get_num_gpus() > 0:
            try:
                gpu_index = faiss.index_cpu_to_gpu(faiss.StandardGpuResources(), 0, index)
                return gpu_index
            except Exception:
                print("GPU not available, using CPU index")
        
        return index

class TextOptimizer:
    """Text processing optimizations"""
    
    def __init__(self):
        self.processed_texts = {}
        
    @lru_cache(maxsize=10000)
    def fast_clean_text(self, text: str) -> str:
        """Optimized text cleaning with caching"""
        # Basic cleaning operations
        text = text.strip()
        text = ' '.join(text.split())  # Normalize whitespace
        return text
    
    def batch_process_texts(self, texts: List[str]) -> List[str]:
        """Process multiple texts efficiently"""
        # Use list comprehension for speed
        return [self.fast_clean_text(text) for text in texts]

class ChunkOptimizer:
    """Optimize chunk processing for better performance"""
    
    def __init__(self):
        self.chunk_cache = {}
        
    def optimize_chunks(self, chunks: List[Dict]) -> List[Dict]:
        """Optimize chunk data structures"""
        optimized_chunks = []
        
        for chunk in chunks:
            # Create optimized chunk with minimal memory footprint
            optimized_chunk = {
                'text': chunk['text'],
                'raw_text': chunk['raw_text'],
                'page': chunk['page'],
                'metadata': chunk.get('metadata', [])
            }
            
            # Remove unnecessary fields to save memory
            if 'section' in chunk and chunk['section'] == 1:
                # Only store section if it's not the default
                pass
            else:
                optimized_chunk['section'] = chunk.get('section', 1)
            
            optimized_chunks.append(optimized_chunk)
        
        return optimized_chunks

class PerformanceMonitor:
    """Monitor and track performance metrics"""
    
    def __init__(self):
        self.metrics = {
            'request_count': 0,
            'total_processing_time': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'embedding_generation_time': 0,
            'retrieval_time': 0,
            'llm_call_time': 0
        }
        self.start_time = time.time()
    
    def record_request(self, processing_time: float):
        """Record a request processing time"""
        self.metrics['request_count'] += 1
        self.metrics['total_processing_time'] += processing_time
    
    def record_cache_hit(self):
        """Record a cache hit"""
        self.metrics['cache_hits'] += 1
    
    def record_cache_miss(self):
        """Record a cache miss"""
        self.metrics['cache_misses'] += 1
    
    def record_embedding_time(self, time_taken: float):
        """Record embedding generation time"""
        self.metrics['embedding_generation_time'] += time_taken
    
    def record_retrieval_time(self, time_taken: float):
        """Record retrieval time"""
        self.metrics['retrieval_time'] += time_taken
    
    def record_llm_time(self, time_taken: float):
        """Record LLM call time"""
        self.metrics['llm_call_time'] += time_taken
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        uptime = time.time() - self.start_time
        total_requests = self.metrics['request_count']
        
        stats = {
            'uptime_seconds': uptime,
            'total_requests': total_requests,
            'requests_per_second': total_requests / uptime if uptime > 0 else 0,
            'average_processing_time': (
                self.metrics['total_processing_time'] / total_requests 
                if total_requests > 0 else 0
            ),
            'cache_hit_rate': (
                self.metrics['cache_hits'] / (self.metrics['cache_hits'] + self.metrics['cache_misses'])
                if (self.metrics['cache_hits'] + self.metrics['cache_misses']) > 0 else 0
            ),
            'average_embedding_time': (
                self.metrics['embedding_generation_time'] / total_requests
                if total_requests > 0 else 0
            ),
            'average_retrieval_time': (
                self.metrics['retrieval_time'] / total_requests
                if total_requests > 0 else 0
            ),
            'average_llm_time': (
                self.metrics['llm_call_time'] / total_requests
                if total_requests > 0 else 0
            )
        }
        
        return stats

# Global instances
memory_optimizer = MemoryOptimizer()
embedding_cache = EmbeddingCache()
text_optimizer = TextOptimizer()
chunk_optimizer = ChunkOptimizer()
performance_monitor = PerformanceMonitor()

# Utility functions
async def optimize_for_performance():
    """Run performance optimizations"""
    # Cleanup old cache files
    embedding_cache.cleanup_old_cache()
    
    # Check memory usage and cleanup if needed
    if memory_optimizer.should_cleanup_memory():
        memory_optimizer.cleanup_memory()

def get_comprehensive_stats() -> Dict[str, Any]:
    """Get all performance and optimization statistics"""
    return {
        'memory_stats': memory_optimizer.get_memory_stats().__dict__,
        'performance_stats': performance_monitor.get_performance_stats(),
        'cache_stats': {
            'embedding_cache_size': len(embedding_cache.memory_cache),
            'chunk_cache_size': len(chunk_optimizer.chunk_cache)
        }
    }

# Export for use in main application
__all__ = [
    'memory_optimizer', 'embedding_cache', 'text_optimizer', 
    'chunk_optimizer', 'performance_monitor', 'FAISSOptimizer',
    'optimize_for_performance', 'get_comprehensive_stats'
]
