"""
Redis Configuration and Management for Insurance Assistant
Provides high-performance caching with intelligent invalidation strategies
"""

import redis.asyncio as redis
import pickle
import json
import hashlib
import time
from typing import Any, Optional, Dict, List
import asyncio
import os
from datetime import datetime, timedelta

class RedisManager:
    """Advanced Redis cache manager with intelligent invalidation"""
    
    def __init__(self, redis_url: str = None):
        self.redis_url = redis_url or os.getenv("REDIS_URL", "redis://localhost:6379")
        self.client = None
        self.connected = False
        
        # Cache configuration
        self.default_ttl = 3600  # 1 hour
        self.document_cache_ttl = 7200  # 2 hours for document processing
        self.embedding_cache_ttl = 86400  # 24 hours for embeddings
        self.api_response_ttl = 1800  # 30 minutes for API responses
        
        # Cache key prefixes for organization
        self.prefixes = {
            'document': 'doc:',
            'embedding': 'emb:',
            'api_response': 'api:',
            'chunk': 'chunk:',
            'metadata': 'meta:',
            'stats': 'stats:'
        }
    
    async def connect(self):
        """Initialize Redis connection with optimized settings"""
        try:
            self.client = redis.from_url(
                self.redis_url,
                decode_responses=False,  # Handle binary data for pickled objects
                max_connections=50,      # Increased connection pool
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30
            )
            
            # Test connection
            await self.client.ping()
            self.connected = True
            
            # Set up cache statistics
            await self.init_stats()
            
            print(f"✅ Redis connected successfully to {self.redis_url}")
            return True
            
        except Exception as e:
            print(f"❌ Redis connection failed: {e}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """Clean disconnect from Redis"""
        if self.client:
            await self.client.close()
            self.connected = False
    
    async def init_stats(self):
        """Initialize cache statistics tracking"""
        stats_key = f"{self.prefixes['stats']}global"
        if not await self.client.exists(stats_key):
            initial_stats = {
                'hits': 0,
                'misses': 0,
                'sets': 0,
                'deletes': 0,
                'created_at': datetime.now().isoformat()
            }
            await self.client.hset(stats_key, mapping=initial_stats)
    
    async def get_stats(self) -> Dict:
        """Get cache performance statistics"""
        if not self.connected:
            return {}
        
        stats_key = f"{self.prefixes['stats']}global"
        stats = await self.client.hgetall(stats_key)
        
        # Convert bytes to strings and calculate hit rate
        decoded_stats = {k.decode(): v.decode() for k, v in stats.items()}
        
        hits = int(decoded_stats.get('hits', 0))
        misses = int(decoded_stats.get('misses', 0))
        total_requests = hits + misses
        hit_rate = (hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **decoded_stats,
            'hit_rate_percent': round(hit_rate, 2),
            'total_requests': total_requests
        }
    
    async def increment_stat(self, stat_name: str):
        """Increment a cache statistic"""
        if self.connected:
            stats_key = f"{self.prefixes['stats']}global"
            await self.client.hincrby(stats_key, stat_name, 1)
    
    def _serialize_data(self, data: Any) -> bytes:
        """Serialize data for Redis storage"""
        return pickle.dumps(data)
    
    def _deserialize_data(self, data: bytes) -> Any:
        """Deserialize data from Redis"""
        return pickle.loads(data)
    
    def _generate_key(self, prefix: str, identifier: str) -> str:
        """Generate a cache key with prefix"""
        return f"{prefix}{identifier}"
    
    async def set_document_cache(self, url: str, data: Dict, ttl: int = None) -> bool:
        """Cache document processing results"""
        if not self.connected:
            return False
        
        try:
            cache_key = hashlib.md5(url.encode()).hexdigest()
            key = self._generate_key(self.prefixes['document'], cache_key)
            
            # Add metadata
            cache_data = {
                'data': data,
                'url': url,
                'cached_at': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            serialized = self._serialize_data(cache_data)
            ttl = ttl or self.document_cache_ttl
            
            await self.client.setex(key, ttl, serialized)
            await self.increment_stat('sets')
            
            return True
            
        except Exception as e:
            print(f"Error setting document cache: {e}")
            return False
    
    async def get_document_cache(self, url: str) -> Optional[Dict]:
        """Retrieve cached document processing results"""
        if not self.connected:
            return None
        
        try:
            cache_key = hashlib.md5(url.encode()).hexdigest()
            key = self._generate_key(self.prefixes['document'], cache_key)
            
            cached_data = await self.client.get(key)
            if cached_data:
                await self.increment_stat('hits')
                cache_obj = self._deserialize_data(cached_data)
                return cache_obj['data']
            else:
                await self.increment_stat('misses')
                return None
                
        except Exception as e:
            print(f"Error getting document cache: {e}")
            await self.increment_stat('misses')
            return None
    
    async def set_api_response_cache(self, prompt_hash: str, response: str, ttl: int = None) -> bool:
        """Cache API responses for identical prompts"""
        if not self.connected:
            return False
        
        try:
            key = self._generate_key(self.prefixes['api_response'], prompt_hash)
            
            cache_data = {
                'response': response,
                'cached_at': datetime.now().isoformat()
            }
            
            serialized = self._serialize_data(cache_data)
            ttl = ttl or self.api_response_ttl
            
            await self.client.setex(key, ttl, serialized)
            await self.increment_stat('sets')
            
            return True
            
        except Exception as e:
            print(f"Error setting API response cache: {e}")
            return False
    
    async def get_api_response_cache(self, prompt_hash: str) -> Optional[str]:
        """Retrieve cached API response"""
        if not self.connected:
            return None
        
        try:
            key = self._generate_key(self.prefixes['api_response'], prompt_hash)
            
            cached_data = await self.client.get(key)
            if cached_data:
                await self.increment_stat('hits')
                cache_obj = self._deserialize_data(cached_data)
                return cache_obj['response']
            else:
                await self.increment_stat('misses')
                return None
                
        except Exception as e:
            print(f"Error getting API response cache: {e}")
            await self.increment_stat('misses')
            return None
    
    async def invalidate_document_cache(self, url: str) -> bool:
        """Invalidate cache for a specific document"""
        if not self.connected:
            return False
        
        try:
            cache_key = hashlib.md5(url.encode()).hexdigest()
            key = self._generate_key(self.prefixes['document'], cache_key)
            
            deleted = await self.client.delete(key)
            if deleted:
                await self.increment_stat('deletes')
            
            return bool(deleted)
            
        except Exception as e:
            print(f"Error invalidating document cache: {e}")
            return False
    
    async def clear_expired_cache(self) -> int:
        """Clear all expired cache entries (Redis handles this automatically, but useful for stats)"""
        if not self.connected:
            return 0
        
        try:
            # Get all keys with TTL
            all_keys = await self.client.keys("*")
            expired_count = 0
            
            for key in all_keys:
                ttl = await self.client.ttl(key)
                if ttl == -2:  # Key doesn't exist (expired)
                    expired_count += 1
            
            return expired_count
            
        except Exception as e:
            print(f"Error checking expired cache: {e}")
            return 0
    
    async def get_cache_info(self) -> Dict:
        """Get comprehensive cache information"""
        if not self.connected:
            return {"status": "disconnected"}
        
        try:
            info = await self.client.info()
            stats = await self.get_stats()
            
            # Get memory usage
            memory_usage = info.get('used_memory_human', 'Unknown')
            connected_clients = info.get('connected_clients', 0)
            
            # Count keys by prefix
            key_counts = {}
            for prefix_name, prefix in self.prefixes.items():
                keys = await self.client.keys(f"{prefix}*")
                key_counts[prefix_name] = len(keys)
            
            return {
                "status": "connected",
                "memory_usage": memory_usage,
                "connected_clients": connected_clients,
                "key_counts": key_counts,
                "performance_stats": stats,
                "redis_version": info.get('redis_version', 'Unknown')
            }
            
        except Exception as e:
            return {"status": "error", "error": str(e)}

# Global Redis manager instance
redis_manager = RedisManager()

# Convenience functions for easy integration
async def init_redis_cache(redis_url: str = None) -> bool:
    """Initialize Redis cache manager"""
    if redis_url:
        redis_manager.redis_url = redis_url
    return await redis_manager.connect()

async def cleanup_redis_cache():
    """Cleanup Redis connections"""
    await redis_manager.disconnect()

# Export for use in main application
__all__ = ['RedisManager', 'redis_manager', 'init_redis_cache', 'cleanup_redis_cache']
