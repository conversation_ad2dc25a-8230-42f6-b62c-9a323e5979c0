<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Insurance Assistant - Streaming Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="url"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.processing {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.complete {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .results {
            margin-top: 20px;
        }
        .question-result {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .question {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .answer {
            color: #555;
            line-height: 1.5;
        }
        .performance-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #e7f3ff;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Insurance Assistant - Streaming Demo</h1>
        <p>Experience real-time document processing with our optimized async backend!</p>
        
        <form id="queryForm">
            <div class="form-group">
                <label for="documentUrl">Document URL:</label>
                <input type="url" id="documentUrl" required 
                       placeholder="https://example.com/insurance-policy.pdf"
                       value="https://www.icicilombard.com/docs/default-source/policy-wordings/motor-insurance/private-car/icicilombard-motor-private-car-policy-wording.pdf">
            </div>
            
            <div class="form-group">
                <label for="questions">Questions (one per line):</label>
                <textarea id="questions" required placeholder="What is covered under this policy?
What are the exclusions?
How do I file a claim?">What is covered under this policy?
What are the exclusions?
How do I file a claim?</textarea>
            </div>
            
            <button type="submit" id="submitBtn">
                <span id="btnText">🔍 Process Document (Streaming)</span>
                <span id="spinner" class="spinner" style="display: none;"></span>
            </button>
        </form>

        <div id="status" class="status" style="display: none;"></div>
        
        <div class="performance-info">
            <h3>🚀 Performance Optimizations Active:</h3>
            <ul>
                <li><strong>Async I/O:</strong> Non-blocking document fetching and API calls</li>
                <li><strong>Streaming Responses:</strong> Real-time progress updates</li>
                <li><strong>Connection Pooling:</strong> Reused HTTP connections</li>
                <li><strong>Redis Caching:</strong> Lightning-fast cache lookups</li>
                <li><strong>Batch Processing:</strong> Concurrent question processing</li>
                <li><strong>Smart Chunking:</strong> Insurance-optimized text segmentation</li>
            </ul>
        </div>

        <div id="results" class="results"></div>
    </div>

    <script>
        const form = document.getElementById('queryForm');
        const submitBtn = document.getElementById('submitBtn');
        const btnText = document.getElementById('btnText');
        const spinner = document.getElementById('spinner');
        const status = document.getElementById('status');
        const results = document.getElementById('results');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const documentUrl = document.getElementById('documentUrl').value;
            const questionsText = document.getElementById('questions').value;
            const questions = questionsText.split('\n').filter(q => q.trim());

            // Reset UI
            submitBtn.disabled = true;
            btnText.style.display = 'none';
            spinner.style.display = 'inline-block';
            results.innerHTML = '';
            
            // Show status
            status.style.display = 'block';
            status.className = 'status processing';
            status.innerHTML = '<div class="spinner"></div> Starting document processing...';

            try {
                // Use streaming endpoint
                const response = await fetch('/api/v1/hackrx/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer d1b791fa0ef5092d9cd051b2b09df2473d1e2ea07e09fe6c61abb5722dfbc7d3'
                    },
                    body: JSON.stringify({
                        documents: documentUrl,
                        questions: questions
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                const answers = new Array(questions.length).fill('');

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                if (data.status === 'processing') {
                                    status.innerHTML = `<div class="spinner"></div> ${data.message}`;
                                } else if (data.status === 'cache_hit') {
                                    status.innerHTML = `✅ ${data.message}`;
                                } else if (data.status === 'processing_question') {
                                    status.innerHTML = `<div class="spinner"></div> Processing: ${data.question}`;
                                } else if (data.status === 'answer') {
                                    answers[data.question_index] = data.answer;
                                    updateResults(questions, answers);
                                } else if (data.status === 'complete') {
                                    status.className = 'status complete';
                                    status.innerHTML = '✅ Processing complete!';
                                }
                            } catch (e) {
                                console.error('Error parsing SSE data:', e);
                            }
                        }
                    }
                }

            } catch (error) {
                status.className = 'status error';
                status.innerHTML = `❌ Error: ${error.message}`;
            } finally {
                submitBtn.disabled = false;
                btnText.style.display = 'inline-block';
                spinner.style.display = 'none';
            }
        });

        function updateResults(questions, answers) {
            results.innerHTML = '';
            questions.forEach((question, index) => {
                const div = document.createElement('div');
                div.className = 'question-result';
                div.innerHTML = `
                    <div class="question">Q${index + 1}: ${question}</div>
                    <div class="answer">${answers[index] || '<em>Processing...</em>'}</div>
                `;
                results.appendChild(div);
            });
        }
    </script>
</body>
</html>
